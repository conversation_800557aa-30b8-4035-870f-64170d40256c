import React, { useState } from "react"
import { TouchableOpacity, Text } from "react-native"
import { Drawer } from "expo-router/drawer"
import CustomDrawer from "@/components/CustomDrawer"
import { LoadingScreen } from "@/components/LoadingScreen"
import { ChatErrorBoundary } from "@/components/ErrorBoundary/ChatErrorBoundary"
import { useRouter } from "expo-router"
import { useQuery } from "convex/react"
import { useConvexAuth } from "convex/react"
import { api } from "convex/_generated/api"
import { Redirect } from "expo-router"

export default function ChatScreen() {
  const router = useRouter()

  const { isAuthenticated, isLoading } = useConvexAuth()

  const [searchQuery, setSearchQuery] = useState("")

  // Always call hooks in the same order. Gate the query with "skip" when the user is
  // not authenticated so the hook call is still executed after sign-out.
  const filteredThreads = useQuery(
    api.chat.searchThreadsByTitle,
    isAuthenticated ? { searchQuery } : "skip",
  )

  // Show loading screen while authentication state is being determined
  if (isLoading) {
    return <LoadingScreen message="Checking authentication..." />
  }

  // Redirect unauthenticated users after hooks have been called.
  if (!isAuthenticated) {
    return <Redirect href={"/sign-in"} />
  }

  const handleLogin = () => {
    router.push("/sign-in")
  }

  return (
    <ChatErrorBoundary>
      <Drawer
        screenOptions={{
          headerShown: true,
        }}
        drawerContent={(props) => (
          <CustomDrawer
            {...props}
            chatThreads={filteredThreads}
            onLogin={handleLogin}
            onSearchChange={setSearchQuery}
            searchQuery={searchQuery}
          />
        )}
      >
        <Drawer.Screen
          name="index"
          options={{
            drawerLabel: "Create New Chat",
            title: "Create New Chat",
          }}
        />
        <Drawer.Screen
          name="[threadId]"
          options={{
            drawerLabel: "Chat Thread",
            title: "Chat Thread",
            headerRight: () => (
              <TouchableOpacity
                style={{
                  backgroundColor: "#D97D54",
                  borderRadius: 8,
                  paddingHorizontal: 12,
                  paddingVertical: 6,
                  marginRight: 16,
                }}
                onPress={() => router.push("/")}
              >
                <Text style={{ color: "#fff", fontWeight: "600" }}>New Chat</Text>
              </TouchableOpacity>
            ),
          }}
        />
      </Drawer>
    </ChatErrorBoundary>
  )
}
